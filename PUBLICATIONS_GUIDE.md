# Publications Management Guide

This guide explains how to add, edit, and format publications in the portfolio website.

## Data Structure

Publications are stored in `src/data/portfolio.ts` in the `publications` array. Each publication follows the `Publication` interface:

```typescript
export interface Publication {
  id: number;
  authors: string;
  year: number;
  title: string;
  journal: string;
  notes: string;
  thumbnail: string;
  type: 'published' | 'unpublished';
  // Enhanced fields for rich formatting
  authorsHtml?: string; // Optional HTML version of authors
  titleHtml?: string;   // Optional HTML version of title
  journalHtml?: string; // Optional HTML version of journal
  notesHtml?: string;   // Optional HTML version of notes
  furtherInfoLink?: string; // Link to more information
  furtherInfoText?: string; // Custom text for the link
}
```

## Adding a New Publication

1. Open `src/data/portfolio.ts`
2. Add a new object to the `publications` array
3. Use the next available ID number
4. Fill in all required fields
5. Optionally add HTML formatting fields

### Basic Example

```typescript
{
  id: 6,
  authors: "<PERSON><PERSON><PERSON>, New Collaborator",
  year: 2025,
  title: "New Research Paper Title",
  journal: "Journal Name",
  notes: "Additional notes about the paper.",
  thumbnail: "@/assets/images/publication-thumb.svg",
  type: "published"
}
```

### Advanced Example with HTML Formatting

```typescript
{
  id: 6,
  authors: "Nguyen Cong Thanh, New Collaborator",
  year: 2025,
  title: "New Research Paper Title",
  journal: "Journal Name",
  notes: "Additional notes about the paper.",
  thumbnail: "@/assets/images/publication-thumb.svg",
  type: "published",
  authorsHtml: "<strong>Nguyen Cong Thanh</strong>, New Collaborator",
  titleHtml: "<em>New Research Paper Title</em>",
  journalHtml: "<strong>Journal Name</strong>",
  notesHtml: "<em>Additional notes about the paper.</em>",
  furtherInfoLink: "https://example.com/paper-details",
  furtherInfoText: "view full paper"
}
```

## HTML Formatting Options

You can use the following HTML tags in the `*Html` fields:

- `<strong>` - Bold text (typically for author names, journal names)
- `<em>` - Italic text (typically for paper titles, notes)
- `<a href="...">` - Links (though `furtherInfoLink` is preferred for main links)
- `<span class="...">` - Custom styling (use Tailwind classes)

### Formatting Guidelines

1. **Author Names**: Bold the main author (usually Nguyen Cong Thanh)
   ```html
   "<strong>Nguyen Cong Thanh</strong>, Co-Author Name"
   ```

2. **Paper Titles**: Use italics for academic papers
   ```html
   "<em>Paper Title Here</em>"
   ```

3. **Journal Names**: Bold journal names
   ```html
   "<strong>Journal of Statistical Research</strong>"
   ```

4. **Notes**: Use italics for additional information
   ```html
   "<em>Open data and code available upon request.</em>"
   ```

## Publication Types

- `"published"` - For peer-reviewed, published papers
- `"unpublished"` - For working papers, preprints, manuscripts under review

## Further Information Links

Use `furtherInfoLink` and `furtherInfoText` to add links to:
- Paper PDFs
- Journal pages
- Preprint servers (arXiv, SSRN, etc.)
- Data repositories
- Code repositories

Example:
```typescript
furtherInfoLink: "https://doi.org/10.1000/journal.2024.123456",
furtherInfoText: "view publication"
```

## Thumbnail Images

Currently, all publications use the placeholder image:
```typescript
thumbnail: "@/assets/images/publication-thumb.svg"
```

To add custom thumbnails:
1. Add image files to `src/assets/images/`
2. Import them at the top of the file
3. Reference them in the publication object

## Testing Changes

After adding or modifying publications:

1. Build the project: `pnpm run build`
2. Preview the site: `pnpm run preview`
3. Check the Publications section at `http://localhost:4321/#publications`
4. Verify formatting renders correctly
5. Test responsive design on different screen sizes

## Troubleshooting

### HTML Not Rendering
- Ensure you're using the `*Html` fields (e.g., `titleHtml` not `title`)
- Check that HTML tags are properly closed
- Verify quotes are escaped correctly in TypeScript strings

### Build Errors
- Run `pnpm run astro -- check` to identify TypeScript issues
- Ensure all required fields are present
- Check for syntax errors in the publications array

### Styling Issues
- Use browser developer tools to inspect the rendered HTML
- Verify Tailwind classes are applied correctly
- Check that custom HTML doesn't break the layout
