---
import { Image } from 'astro:assets';
import type { Publication } from '@/data/portfolio';
import publicationThumb from '@/assets/images/publication-thumb.svg';

interface Props {
  publications: Publication[];
}

const { publications } = Astro.props;

const publishedPapers = publications.filter(pub => pub.type === 'published');
const unpublishedPapers = publications.filter(pub => pub.type === 'unpublished');
---

<section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
  <div class="flex items-center justify-between">
    <h2 id="pub-title" class="text-2xl text-sky-900 font-extrabold">Academic Peer-Reviewed Publications</h2>
  </div>

  <!-- Published Papers -->
  {publishedPapers.length > 0 && (
    <div class="mt-6">
      <h3 class="text-sky-900 font-bold">Published Papers</h3>
      <ol class="relative mt-4 space-y-6" role="list">
        {publishedPapers.map((publication, index) => (
          <li class="pl-14" aria-label={`Publication ${index + 1}`}>
            <div class="absolute left-0 top-0 flex items-center" aria-hidden="true">
              <div class="w-10 h-10 rounded-full bg-sky-100 border border-sky-300 text-sky-900 grid place-items-center font-bold">
                {String(index + 1).padStart(2, '0')}
              </div>
            </div>
            <article class="grid grid-cols-1 md:grid-cols-[120px,1fr] gap-4 bg-white border border-slate-200 rounded p-4">
              <div class="w-full h-[90px] md:h-[96px] overflow-hidden rounded bg-slate-50 grid place-items-center">
                <Image src={publicationThumb} width={120} height={90} alt="Publication thumbnail placeholder" />
              </div>
              <div>
                <header>
                  <p class="text-xs text-slate-500">{publication.authors} — {publication.year}</p>
                  <h4 class="text-sky-900 font-semibold">{publication.title}</h4>
                  <p class="text-sm">{publication.journal}</p>
                </header>
                <p class="mt-2 text-xs text-slate-600">Notes: {publication.notes}</p>
              </div>
            </article>
          </li>
        ))}
      </ol>
    </div>
  )}

  <!-- Unpublished Papers -->
  {unpublishedPapers.length > 0 && (
    <div class="mt-10">
      <h3 class="text-sky-900 font-bold">Unpublished Papers</h3>
      <ol class="relative mt-4 space-y-6" role="list">
        {unpublishedPapers.map((publication, index) => (
          <li class="pl-14" aria-label={`Working paper ${index + 1}`}>
            <div class="absolute left-0 top-0 flex items-center" aria-hidden="true">
              <div class="w-10 h-10 rounded-full bg-amber-50 border border-amber-300 text-amber-900 grid place-items-center font-bold">
                {String(index + 1).padStart(2, '0')}
              </div>
            </div>
            <article class="grid grid-cols-1 md:grid-cols-[120px,1fr] gap-4 bg-white border border-slate-200 rounded p-4">
              <div class="w-full h-[90px] md:h-[96px] overflow-hidden rounded bg-slate-50 grid place-items-center">
                <Image src={publicationThumb} width={120} height={90} alt="Working paper thumbnail placeholder" />
              </div>
              <div>
                <header>
                  <p class="text-xs text-slate-500">{publication.authors} — {publication.year}</p>
                  <h4 class="text-sky-900 font-semibold">{publication.title}</h4>
                  <p class="text-sm">{publication.journal}</p>
                </header>
                <p class="mt-2 text-xs text-slate-600">Notes: {publication.notes}</p>
              </div>
            </article>
          </li>
        ))}
      </ol>
    </div>
  )}
</section>
