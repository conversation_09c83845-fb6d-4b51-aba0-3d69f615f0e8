---
import type { NavigationItem } from '@/data/portfolio';

interface Props {
  brandName: string;
  navigation: NavigationItem[];
  skipToContentText: string;
}

const { brandName, navigation, skipToContentText } = Astro.props;
---

<a class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-white border border-slate-300 p-2 rounded" href="#main">{skipToContentText}</a>

<header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-white/70 bg-white/90 border-b border-slate-200">
  <nav class="mx-auto max-w-6xl px-4 sm:px-6 py-3 flex items-center justify-between" aria-label="Main navigation">
    <div class="text-sky-900 font-bold tracking-tight">{brandName}</div>
    <ul class="flex gap-6 text-sm" role="list">
      {navigation.map((item) => (
        <li>
          <a 
            href={item.href} 
            class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded"
          >
            {item.label}
          </a>
        </li>
      ))}
    </ul>
  </nav>
</header>
