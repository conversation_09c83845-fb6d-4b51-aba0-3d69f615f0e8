---
import { Image } from 'astro:assets';
import type { PersonalInfo, ResearchField } from '@/data/portfolio';
import profilePlaceholder from '@/assets/images/profile-placeholder.svg';

interface Props {
  personalInfo: PersonalInfo;
  researchFields: ResearchField[];
}

const { personalInfo, researchFields } = Astro.props;
---

<section id="home" class="pt-10 sm:pt-14 pb-10">
  <div class="grid grid-cols-1 md:grid-cols-[260px,1fr] gap-8 items-start">
    <figure class="mx-auto md:mx-0 w-[220px] h-[220px] md:w-[240px] md:h-[240px] rounded-full ring-4 ring-sky-800/80 ring-offset-4 ring-offset-slate-100 overflow-hidden bg-slate-100 flex items-center justify-center">
      <Image src={profilePlaceholder} alt={`Profile picture placeholder for ${personalInfo.name}`} width={240} height={240} class="object-cover" />
    </figure>

    <div>
      <h1 class="text-3xl sm:text-4xl text-sky-900 font-extrabold">{personalInfo.name}</h1>
      <p class="mt-1 text-slate-600 italic">{personalInfo.jobTitle}</p>

      <dl class="mt-5 grid grid-cols-1 sm:grid-cols-2 gap-3" aria-label="Professional contact information">
        <div class="bg-white border border-slate-200 rounded px-3 py-2">
          <dt class="sr-only">Email</dt>
          <dd class="text-sm">Email: {personalInfo.email}</dd>
        </div>
        <div class="bg-white border border-slate-200 rounded px-3 py-2">
          <dt class="sr-only">Phone</dt>
          <dd class="text-sm">Phone: {personalInfo.phone}</dd>
        </div>
        <div class="bg-white border border-slate-200 rounded px-3 py-2 sm:col-span-2">
          <dt class="sr-only">Address</dt>
          <dd class="text-sm">Address: {personalInfo.address}</dd>
        </div>
      </dl>

      <div class="mt-4">
        <h2 class="text-sky-900 font-bold">Research Fields</h2>
        <ul class="mt-2 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
          {researchFields.map((field) => (
            <li>
              <span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">
                {field.name}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </div>
</section>
